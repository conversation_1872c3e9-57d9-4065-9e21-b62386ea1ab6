import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { MapPin, Phone, Mail, Send } from "lucide-react"
import { useState } from "react"

export function Contact() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: ""
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log("Form submitted:", formData)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: "smooth" })
    }
  }

  return (
    <section id="contact" className="py-20 px-6 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-10 left-10 w-60 h-60 bg-accent/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-primary/10 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="text-gradient-primary">Get in</span>{" "}
            <span className="text-gradient-accent">Touch</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Have a question or project in mind? We'd love to hear from you.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-8 animate-fade-in" style={{ animationDelay: "0.2s" }}>
            <h3 className="text-2xl font-bold text-gradient-primary mb-8">Contact Information</h3>
            
            {[
              {
                icon: Phone,
                title: "Phone",
                content: "+91 79724 25585",
                link: "tel:+************"
              },
              {
                icon: Mail,
                title: "Email", 
                content: "<EMAIL>",
                link: "mailto:<EMAIL>"
              },
              {
                icon: MapPin,
                title: "Location",
                content: "DLF Westend Ave DLF New Town Akshaynagar 3rd Floor, Bangaluru, Karnataka, India - 560068"
              }
            ].map((item, index) => {
              const Icon = item.icon
              return (
                <div 
                  key={item.title}
                  className="glass-card p-6 hover:glow-primary transition-all duration-500"
                >
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-accent rounded-xl flex items-center justify-center flex-shrink-0">
                      <Icon className="w-6 h-6 text-accent-foreground" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gradient-primary mb-2">{item.title}</h4>
                      {item.link ? (
                        <a 
                          href={item.link}
                          className="text-muted-foreground hover:text-accent transition-colors"
                        >
                          {item.content}
                        </a>
                      ) : (
                        <p className="text-muted-foreground">{item.content}</p>
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          {/* Contact Form */}
          <div className="animate-fade-in" style={{ animationDelay: "0.4s" }}>
            <form onSubmit={handleSubmit} className="glass-card p-8 space-y-6">
              <h3 className="text-2xl font-bold text-gradient-accent mb-6">Send us a Message</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    required
                    value={formData.name}
                    onChange={handleChange}
                    className="glass border-glass-border"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    name="email" 
                    type="email"
                    required
                    value={formData.email}
                    onChange={handleChange}
                    className="glass border-glass-border"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleChange}
                    className="glass border-glass-border"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="subject">Subject *</Label>
                  <Input
                    id="subject"
                    name="subject"
                    required
                    value={formData.subject}
                    onChange={handleChange}
                    className="glass border-glass-border"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Message *</Label>
                <Textarea
                  id="message"
                  name="message"
                  required
                  rows={5}
                  value={formData.message}
                  onChange={handleChange}
                  className="glass border-glass-border resize-none"
                />
              </div>

              <Button type="submit" variant="gradient" size="lg" className="w-full group">
                Send Message
                <Send className="w-4 h-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </form>
          </div>
        </div>

        {/* Quick Links */}
        <div className="text-center mt-16 animate-fade-in" style={{ animationDelay: "0.6s" }}>
          <h3 className="text-xl font-semibold text-gradient-primary mb-6">Quick Links</h3>
          <div className="flex flex-wrap items-center justify-center gap-4">
            {[
              { name: "Services", id: "services" },
              { name: "Career", id: "contact" },
              { name: "About", id: "about" },
              { name: "Contact", id: "contact" },
              { name: "Schedule a Consultation", id: "contact" }
            ].map((link, index) => (
              <button
                key={link.name}
                onClick={() => scrollToSection(link.id)}
                className="text-muted-foreground hover:text-accent transition-colors text-sm font-medium"
              >
                {link.name}
                {index < 4 && <span className="ml-4 text-muted-foreground/50">|</span>}
              </button>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}