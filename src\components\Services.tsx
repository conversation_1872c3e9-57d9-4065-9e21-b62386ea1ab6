import { 
  Users, 
  Palette, 
  Code, 
  Megaphone, 
  ShoppingCart, 
  Award, 
  Lightbulb, 
  Search, 
  Cloud 
} from "lucide-react"

const services = [
  {
    icon: Code,
    title: "Backend Architecture",
    description: "Scalable, secure, and optimized backend systems for your applications."
  },
  {
    icon: Palette,
    title: "UI/UX and Graphics Designing", 
    description: "Create stunning user experiences with our expert design services."
  },
  {
    icon: Code,
    title: "Website Development",
    description: "Build powerful and responsive websites tailored to your needs."
  },
  {
    icon: Megaphone,
    title: "Digital Marketing",
    description: "Boost your online presence with strategic digital marketing solutions."
  },
  {
    icon: ShoppingCart,
    title: "Ecommerce Development",
    description: "Launch and scale your online store with our ecommerce solutions."
  },
  {
    icon: Award,
    title: "Corporate Branding",
    description: "Establish a strong brand identity that resonates with your audience."
  },
  {
    icon: Lightbulb,
    title: "IT Consultancy", 
    description: "Get expert guidance for your technology decisions and strategy."
  },
  {
    icon: Search,
    title: "SEO",
    description: "Improve your search rankings and drive organic traffic growth."
  },
  {
    icon: Cloud,
    title: "Cloud Solutions",
    description: "Leverage cloud technology for scalable and secure infrastructure."
  }
]

export function Services() {
  return (
    <section id="services" className="py-20 px-6 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-10 right-10 w-40 h-40 bg-primary/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 left-10 w-60 h-60 bg-accent/10 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="text-gradient-primary">Our</span>{" "}
            <span className="text-gradient-accent">Services</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            Discover our comprehensive range of tech solutions designed to accelerate your business growth and drive digital transformation.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const Icon = service.icon
            return (
              <div 
                key={service.title}
                className="glass-card p-8 group hover:glow-primary transition-all duration-500 animate-fade-in transform hover:scale-105"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="mb-6">
                  <div className="w-16 h-16 bg-gradient-accent rounded-xl flex items-center justify-center mb-4 group-hover:animate-pulse">
                    <Icon className="w-8 h-8 text-accent-foreground" />
                  </div>
                  <h3 className="text-xl font-semibold text-gradient-primary mb-3">
                    {service.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {service.description}
                  </p>
                </div>
                
                {/* Hover effect indicator */}
                <div className="h-1 w-0 bg-gradient-accent rounded-full group-hover:w-full transition-all duration-500"></div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}