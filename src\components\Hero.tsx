import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>R<PERSON>, Sparkles } from "lucide-react"

export function Hero() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: "smooth" })
    }
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 animated-bg opacity-70"></div>
      <div className="absolute inset-0 bg-gradient-background/80"></div>
      
      {/* Floating elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-primary/20 rounded-full blur-xl animate-float"></div>
      <div className="absolute top-40 right-20 w-32 h-32 bg-accent/20 rounded-full blur-xl animate-float" style={{ animationDelay: "2s" }}></div>
      <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-secondary/20 rounded-full blur-xl animate-float" style={{ animationDelay: "4s" }}></div>
      
      {/* Content */}
      <div className="relative z-10 text-center max-w-5xl mx-auto px-6">
        <div className="animate-fade-in">
          <div className="flex items-center justify-center gap-2 mb-6">
            <Sparkles className="w-8 h-8 text-accent animate-pulse" />
            <span className="text-accent font-medium tracking-wide">Innovation • Growth • Transformation</span>
            <Sparkles className="w-8 h-8 text-accent animate-pulse" />
          </div>
          
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 animate-slide-up">
            <span className="text-gradient-primary">Empowering</span>{" "}
            <span className="text-gradient-accent">Businesses</span>
            <br />
            <span className="text-gradient-primary">Through</span>{" "}
            <span className="text-gradient-accent">Innovation</span>
          </h1>
          
          <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto leading-relaxed animate-fade-in" style={{ animationDelay: "0.3s" }}>
            Technology solutions that accelerate growth and transformation.
            <br className="hidden md:block" />
            Discover the future of your business with{" "}
            <span className="text-gradient-primary font-semibold inline-block overflow-hidden whitespace-nowrap border-r-2 border-primary animate-typewriter" style={{ animationDelay: "2s" }}>
              Sparkline World Technology
            </span>.
          </p>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 animate-fade-in" style={{ animationDelay: "0.6s" }}>
            <Button 
              variant="hero" 
              size="xl"
              onClick={() => scrollToSection("services")}
              className="group"
            >
              Get Started
              <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
            </Button>
            
            <Button 
              variant="glass-accent" 
              size="xl"
              onClick={() => scrollToSection("contact")}
              className="group"
            >
              Schedule a Consultation
              <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
            </Button>
          </div>
        </div>
      </div>
      
      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-accent/50 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-accent rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  )
}