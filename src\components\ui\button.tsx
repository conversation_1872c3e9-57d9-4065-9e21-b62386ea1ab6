import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground shadow hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline: "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        
        // Glassmorphic variants for Sparkline theme
        glass: "glass border-glass-border text-foreground hover:bg-glass-border/20 backdrop-blur-lg",
        "glass-primary": "glass bg-primary/20 border-primary/30 text-primary-foreground hover:bg-primary/30 hover:shadow-lg",
        "glass-accent": "glass bg-accent/20 border-accent/30 text-accent-foreground hover:bg-accent/30 hover:shadow-lg",
        
        // Gradient variants
        gradient: "bg-gradient-primary text-primary-foreground hover:opacity-90 glow-primary",
        "gradient-accent": "bg-gradient-accent text-accent-foreground hover:opacity-90 glow-accent",
        "gradient-secondary": "bg-gradient-secondary text-secondary-foreground hover:opacity-90 glow-secondary",
        
        // Hero CTA variant
        hero: "glass bg-gradient-primary border-primary/30 text-primary-foreground hover:bg-gradient-accent hover:glow-accent transform hover:scale-105 animate-pulse-glow px-8 py-4 text-base font-semibold rounded-2xl",
        
        // Consultation button variant  
        consultation: "bg-gradient-accent text-accent-foreground hover:bg-gradient-primary glow-accent hover:glow-primary transform hover:scale-105 px-6 py-3 font-medium rounded-xl"
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        xl: "h-12 rounded-xl px-10 text-base",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
