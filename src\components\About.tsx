import { Building, Users, Target, Heart } from "lucide-react"

export function About() {
  return (
    <section id="about" className="py-20 px-6 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-primary/10 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="text-gradient-primary">About</span>{" "}
            <span className="text-gradient-accent">Sparkline Tech Solutions</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            Empowering businesses through innovative technology solutions since 2023.
          </p>
        </div>

        {/* Mission Statement with Logo */}
        <div className="glass-card p-8 mb-16 animate-fade-in" style={{ animationDelay: "0.4s" }}>
          <div className="flex flex-col lg:flex-row items-center gap-8">
            <div className="flex-1">
              <h3 className="text-2xl font-bold text-gradient-primary mb-6 flex items-center gap-3">
                <Building className="w-8 h-8" />
                Our Mission
              </h3>
              <p className="text-lg text-muted-foreground leading-relaxed">
                At Sparkline Tech Solutions, we are committed to driving digital transformation and empowering businesses to thrive in the rapidly evolving technological landscape. Our mission is to deliver cutting-edge solutions that solve complex challenges and create lasting value for our clients.
              </p>
            </div>
            <div className="text-center animate-fade-in" style={{ animationDelay: "0.2s" }}>
              <div className="glass-card inline-flex items-center justify-center w-32 h-32 mb-4">
                <div className="text-4xl font-bold text-gradient-primary">S</div>
              </div>
              <p className="text-sm text-muted-foreground">Sparkline World Technology Logo</p>
            </div>
          </div>
        </div>

        {/* Core Values Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {[
            {
              icon: Heart,
              title: "What we Value",
              description: "We value innovation, integrity, and excellence in all our endeavors."
            },
            {
              icon: Target,
              title: "What we Believe",
              description: "We believe in the power of technology to transform businesses and improve lives."
            },
            {
              icon: Users,
              title: "Who we Are",
              description: "We are a team of passionate technologists dedicated to solving complex challenges."
            },
            {
              icon: Building,
              title: "How we Behave",
              description: "We act with professionalism, respect, and a commitment to continuous improvement."
            }
          ].map((value, index) => {
            const Icon = value.icon
            return (
              <div 
                key={value.title}
                className="glass-card p-8 animate-fade-in hover:glow-accent transition-all duration-500"
                style={{ animationDelay: `${0.6 + index * 0.1}s` }}
              >
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center">
                    <Icon className="w-6 h-6 text-primary-foreground" />
                  </div>
                  <h3 className="text-xl font-semibold text-gradient-accent">
                    {value.title}
                  </h3>
                </div>
                <p className="text-muted-foreground leading-relaxed">
                  {value.description}
                </p>
              </div>
            )
          })}
        </div>

        {/* Company Timeline */}
        <div className="glass-card p-8 animate-fade-in" style={{ animationDelay: "1s" }}>
          <h3 className="text-2xl font-bold text-gradient-primary mb-8 text-center">Our Journey</h3>
          <div className="flex items-center justify-center">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-accent rounded-full flex items-center justify-center mb-4 mx-auto animate-pulse-glow">
                <span className="text-2xl font-bold text-accent-foreground">2023</span>
              </div>
              <p className="text-muted-foreground">
                Founded with a vision to transform businesses through innovative technology solutions
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}