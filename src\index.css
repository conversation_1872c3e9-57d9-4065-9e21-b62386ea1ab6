@tailwind base;
@tailwind components;
@tailwind utilities;

/* Sparkline World Technology Design System - Glassmorphic Tech Theme */

@layer base {
  :root {
    /* Core Brand Colors - Pure Blue & Purple Theme */
    --background: 255 255 255;
    --foreground: 30 41 59;

    /* Glass morphism backgrounds */
    --glass: 255 255 255;
    --glass-border: 226 232 240;
    
    /* Deep Blue Primary */
    --primary: 240 100% 60%;
    --primary-foreground: 255 255 255;
    --primary-glow: 240 100% 65%;
    
    /* Pure Purple Accent */
    --accent: 270 95% 70%;
    --accent-foreground: 255 255 255;
    --accent-glow: 270 95% 75%;
    
    /* Blue Secondary */
    --secondary: 220 100% 65%;
    --secondary-foreground: 255 255 255;
    --secondary-glow: 220 100% 70%;

    /* Muted variants - Blue tinted */
    --muted: 215 28% 92%;
    --muted-foreground: 215 16% 47%;

    /* Card & UI Elements */
    --card: 255 255 255;
    --card-foreground: 30 41 59;
    
    --border: 215 28% 88%;
    --input: 215 28% 88%;
    --ring: 240 100% 60%;

    /* Destructive */
    --destructive: 0 84% 60%;
    --destructive-foreground: 255 255 255;

    --radius: 0.75rem;

    /* Pure Blue-Purple Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(240 100% 60%), hsl(270 95% 70%));
    --gradient-secondary: linear-gradient(135deg, hsl(220 100% 65%), hsl(240 100% 60%));
    --gradient-accent: linear-gradient(135deg, hsl(270 95% 70%), hsl(240 100% 60%));
    --gradient-background: linear-gradient(135deg, hsl(255 255 255), hsl(240 100% 98%));
    
    /* Animated background gradients - Blue/Purple only */
    --animated-bg-1: linear-gradient(-45deg, hsl(240 100% 60% / 0.1), hsl(270 95% 70% / 0.1), hsl(220 100% 65% / 0.1), hsl(260 90% 65% / 0.1));
    
    /* Glass effects */
    --glass-bg: hsl(255 255 255 / 0.1);
    --glass-border: hsl(255 255 255 / 0.2);
    --glass-shadow: 0 8px 32px hsl(240 100% 60% / 0.1);
    
    /* Shadows with blue-purple glow */
    --shadow-glow-primary: 0 0 40px hsl(240 100% 60% / 0.3);
    --shadow-glow-accent: 0 0 40px hsl(270 95% 70% / 0.3);
    --shadow-glow-secondary: 0 0 40px hsl(220 100% 65% / 0.3);
  }

  .dark {
    /* Dark theme - Deep space purple background */
    --background: 262 50% 8%;
    --foreground: 248 250 252;

    --glass: 262 50% 12%;
    --glass-border: 262 50% 20%;
    
    --primary: 240 100% 65%;
    --primary-foreground: 262 50% 8%;
    --primary-glow: 240 100% 70%;
    
    --accent: 280 90% 75%;
    --accent-foreground: 262 50% 8%;
    --accent-glow: 280 90% 80%;
    
    --secondary: 220 100% 70%;
    --secondary-foreground: 262 50% 8%;
    --secondary-glow: 220 100% 75%;

    --muted: 262 50% 15%;
    --muted-foreground: 226 232 240;

    --card: 262 50% 12%;
    --card-foreground: 248 250 252;
    
    --border: 262 50% 20%;
    --input: 262 50% 15%;
    --ring: 262 83% 65%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 248 250 252;

    /* Dark gradients */
    --gradient-background: linear-gradient(135deg, hsl(262 50% 8%), hsl(262 50% 12%));
    --animated-bg-1: linear-gradient(-45deg, hsl(240 100% 60% / 0.2), hsl(280 90% 70% / 0.2), hsl(220 100% 65% / 0.2), hsl(240 100% 65% / 0.2));
    
    /* Enhanced glass effects for dark mode */
    --glass-bg: hsl(255 255 255 / 0.05);
    --glass-border: hsl(255 255 255 / 0.1);
    --glass-shadow: 0 8px 32px hsl(0 0% 0% / 0.3);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground font-sans;
    background: var(--gradient-background);
    overflow-x: hidden;
  }
}

@layer components {
  /* Glass morphism effect */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }
  
  .glass-card {
    @apply glass rounded-2xl;
  }
  
  /* Animated background */
  .animated-bg {
    background: var(--animated-bg-1);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
  }
  
  /* Glow effects */
  .glow-primary {
    box-shadow: var(--shadow-glow-primary);
  }
  
  .glow-accent {
    box-shadow: var(--shadow-glow-accent);
  }
  
  .glow-secondary {
    box-shadow: var(--shadow-glow-secondary);
  }
  
  /* Text gradients */
  .text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-gradient-accent {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

@layer utilities {
  /* Smooth animations */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite alternate;
  }
  
  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.8s ease-out;
  }
  
  /* Typing animation */
  .animate-typewriter {
    animation: typewriter 3s steps(40, end);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulseGlow {
  from {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
  }
  to {
    box-shadow: 0 0 40px hsl(var(--primary) / 0.6);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}