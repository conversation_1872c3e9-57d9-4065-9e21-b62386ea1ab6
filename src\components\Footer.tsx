import { Facebook, Twitter, Linkedin, Instagram, Github, MapPin, Mail, Phone } from "lucide-react"

export function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="glass border-t border-glass-border py-16 px-6">
      <div className="container mx-auto">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
          
          {/* Company Info */}
          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center">
                  <span className="text-lg font-bold text-primary-foreground">S</span>
                </div>
                <div className="text-left">
                  <div className="text-sm font-bold text-gradient-primary leading-tight">
                    SPARKLINE TECH
                  </div>
                  <div className="text-sm font-bold text-gradient-accent leading-tight">
                    SOLUTIONS
                  </div>
                </div>
              </div>
              <p className="text-muted-foreground text-sm leading-relaxed">
                Transforming businesses through innovative technology solutions.
              </p>
            </div>
            
            {/* Social Media Links */}
            <div className="flex items-center gap-3">
              {[
                { icon: Facebook, href: "#", label: "Facebook" },
                { icon: Twitter, href: "#", label: "Twitter" },
                { icon: Linkedin, href: "#", label: "LinkedIn" },
                { icon: Instagram, href: "#", label: "Instagram" },
                { icon: Github, href: "#", label: "GitHub" }
              ].map((social) => {
                const Icon = social.icon
                return (
                  <a
                    key={social.label}
                    href={social.href}
                    aria-label={social.label}
                    className="w-9 h-9 glass-card flex items-center justify-center group hover:glow-accent transition-all duration-300"
                  >
                    <Icon className="w-4 h-4 text-muted-foreground group-hover:text-accent transition-colors" />
                  </a>
                )
              })}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold text-gradient-primary mb-6">Quick Links</h4>
            <div className="space-y-3">
              {[
                { name: "About Us", href: "#about" },
                { name: "Services", href: "#services" },
                { name: "Consultation", href: "#contact" },
                { name: "Careers", href: "#careers" }
              ].map((link) => (
                <a
                  key={link.name}
                  href={link.href}
                  className="block text-muted-foreground hover:text-accent transition-colors duration-200 text-sm"
                >
                  {link.name}
                </a>
              ))}
            </div>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold text-gradient-primary mb-6">Services</h4>
            <div className="space-y-3">
              {[
                { name: "Web Development", href: "#services" },
                { name: "App Development", href: "#services" },
                { name: "UI/UX Design", href: "#services" },
                { name: "Digital Marketing", href: "#services" }
              ].map((service) => (
                <a
                  key={service.name}
                  href={service.href}
                  className="block text-muted-foreground hover:text-accent transition-colors duration-200 text-sm"
                >
                  {service.name}
                </a>
              ))}
            </div>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold text-gradient-primary mb-6">Contact Us</h4>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <MapPin className="w-4 h-4 text-accent mt-1 flex-shrink-0" />
                <p className="text-muted-foreground text-sm leading-relaxed">
                  DLF Westend Ave DLF New Town Akshaynagar 3rd Floor, Bangaluru, Karnataka, India
                </p>
              </div>
              
              <div className="flex items-center gap-3">
                <Mail className="w-4 h-4 text-accent flex-shrink-0" />
                <a 
                  href="mailto:<EMAIL>"
                  className="text-muted-foreground hover:text-accent transition-colors duration-200 text-sm"
                >
                  <EMAIL>
                </a>
              </div>
              
              <div className="flex items-center gap-3">
                <Phone className="w-4 h-4 text-accent flex-shrink-0" />
                <a 
                  href="tel:+************"
                  className="text-muted-foreground hover:text-accent transition-colors duration-200 text-sm"
                >
                  +91 79724 25585
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-glass-border pt-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <p className="text-sm text-muted-foreground text-center md:text-left">
              © {currentYear} Sparkline Tech Solutions. All rights reserved.
            </p>
            
            <div className="flex items-center gap-6">
              <a 
                href="#privacy" 
                className="text-sm text-muted-foreground hover:text-accent transition-colors duration-200"
              >
                Privacy Policy
              </a>
              <span className="text-muted-foreground">•</span>
              <a 
                href="#terms" 
                className="text-sm text-muted-foreground hover:text-accent transition-colors duration-200"
              >
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}