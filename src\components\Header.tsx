import { But<PERSON> } from "@/components/ui/button"
import { ThemeToggle } from "@/components/ThemeToggle"
import { useState, useEffect } from "react"

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: "smooth" })
    }
  }

  return (
    <header 
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? "glass py-4" : "py-6"
      }`}
    >
      <div className="container mx-auto px-6 flex items-center justify-between">
        {/* Left: Schedule Consultation Button */}
        <Button 
          variant="consultation" 
          onClick={() => scrollToSection("contact")}
          className="animate-fade-in"
        >
          Schedule a Consultation
        </Button>

        {/* Center: Navigation Links */}
        <nav className="hidden md:flex items-center gap-8">
          {[
            { name: "Services", id: "services" },
            { name: "Culture", id: "culture" },
            { name: "About", id: "about" },
            { name: "Contact", id: "contact" }
          ].map((item) => (
            <button
              key={item.name}
              onClick={() => scrollToSection(item.id)}
              className="text-foreground hover:text-accent transition-all duration-300 font-medium text-sm tracking-wide hover:scale-105 relative group"
            >
              {item.name}
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-accent transition-all duration-300 group-hover:w-full"></span>
            </button>
          ))}
        </nav>

        {/* Right: Company Name & Theme Toggle */}
        <div className="flex items-center gap-4">
          <div className="text-right">
            <div className="text-lg font-bold text-gradient-primary leading-tight">
              SPARKLINE WORLD
            </div>
            <div className="text-lg font-bold text-gradient-accent leading-tight">
              TECHNOLOGY
            </div>
          </div>
          <ThemeToggle />
        </div>
      </div>
    </header>
  )
}