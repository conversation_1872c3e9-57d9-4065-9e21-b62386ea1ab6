import { Heart, Users, Zap, Target } from "lucide-react"

export function Culture() {
  return (
    <section id="culture" className="py-20 px-6 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-secondary/10 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="text-gradient-primary">Our</span>{" "}
            <span className="text-gradient-accent">Culture</span>
          </h2>
          <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            We foster an environment of innovation, collaboration, and continuous learning.
          </p>
        </div>

        {/* Culture Values Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {[
            {
              icon: Zap,
              title: "Innovation",
              description: "Pushing boundaries with cutting-edge technology"
            },
            {
              icon: Users,
              title: "Collaboration", 
              description: "Working together to achieve extraordinary results"
            },
            {
              icon: Heart,
              title: "Passion",
              description: "Driven by our love for technology and excellence"
            },
            {
              icon: Target,
              title: "Excellence",
              description: "Committed to delivering the highest quality solutions"
            }
          ].map((value, index) => {
            const Icon = value.icon
            return (
              <div 
                key={value.title}
                className="text-center animate-fade-in"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="w-20 h-20 bg-gradient-secondary rounded-full flex items-center justify-center mx-auto mb-6 glow-secondary animate-float">
                  <Icon className="w-10 h-10 text-secondary-foreground" />
                </div>
                <h3 className="text-xl font-semibold text-gradient-primary mb-3">
                  {value.title}
                </h3>
                <p className="text-muted-foreground">
                  {value.description}
                </p>
              </div>
            )
          })}
        </div>

        {/* Culture Image Placeholder */}
        <div className="glass-card p-8 text-center animate-fade-in" style={{ animationDelay: "0.8s" }}>
          <div className="w-full h-64 bg-gradient-accent/20 rounded-2xl flex items-center justify-center mb-6">
            <div className="text-6xl text-gradient-accent">🚀</div>
          </div>
          <p className="text-lg text-muted-foreground">
            "Innovation is at the heart of everything we do. We believe in creating a workplace where creativity thrives and breakthrough ideas come to life."
          </p>
          <div className="mt-6">
            <div className="w-16 h-1 bg-gradient-accent mx-auto rounded-full"></div>
          </div>
        </div>
      </div>
    </section>
  )
}