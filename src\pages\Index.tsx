import { Header } from "@/components/Header";
import { <PERSON> } from "@/components/Hero";
import { Services } from "@/components/Services";
import { Culture } from "@/components/Culture";
import { About } from "@/components/About";
import { Contact } from "@/components/Contact";
import { Footer } from "@/components/Footer";

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-background relative overflow-x-hidden">
      {/* Fixed animated background */}
      <div className="fixed inset-0 animated-bg opacity-30 pointer-events-none"></div>
      
      {/* Main content */}
      <div className="relative z-10">
        <Header />
        
        <main>
          <Hero />
          <Services />
          <Culture />
          <About />
          <Contact />
        </main>
        
        <Footer />
      </div>
    </div>
  );
};

export default Index;
